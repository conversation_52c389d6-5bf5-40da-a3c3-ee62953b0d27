/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.inspect.dto.WaveConfigDTO;
import com.snszyk.inspect.dto.WaveConfigMonitorDTO;
import com.snszyk.inspect.service.IWaveConfigService;
import com.snszyk.inspect.vo.WaveConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 设备点检波形配置 控制器
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/waveConfig")
@Api(value = "设备点检波形配置", tags = "设备点检波形配置接口")
public class WaveConfigController extends SzykController {

	private final IWaveConfigService waveConfigService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<WaveConfigDTO> detail(Long id) {
		return R.data(waveConfigService.detail(id));
	}

	/**
	 * 列表 设备点检波形配置
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入parentId、monitorName、equipmentType")
	public R<List<WaveConfigMonitorDTO>> list(@ApiParam(value = "树结构id", required = true) @RequestParam Long parentId,
											  @ApiParam(value = "部位名称") @RequestParam(required = false) String monitorName,
											  @ApiParam(value = "部位类型（字典：equipment_type）") @RequestParam(required = false) Integer equipmentType) {
		return R.data(waveConfigService.monitorList(parentId, monitorName, equipmentType));
	}

	/**
	 * 新增或修改 设备点检波形配置
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增或修改", notes = "传入waveConfig")
	public R submit(@Valid @RequestBody WaveConfigVO waveConfig) {
		return R.status(waveConfigService.submit(waveConfig));
	}

	/**
	 * 删除 设备点检波形配置
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "删除", notes = "传入id")
	public R remove(Long id) {
		return R.status(waveConfigService.remove(id));
	}

}
