/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.inspect.entity.WaveConfig;
import com.snszyk.inspect.vo.WaveConfigVO;

import java.util.Objects;

/**
 * 设备点检波形配置包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public class WaveConfigWrapper extends BaseEntityWrapper<WaveConfig, WaveConfigVO> {

	public static WaveConfigWrapper build() {
		return new WaveConfigWrapper();
 	}

	@Override
	public WaveConfigVO entityVO(WaveConfig waveConfig) {
		WaveConfigVO waveConfigVO = Objects.requireNonNull(BeanUtil.copy(waveConfig, WaveConfigVO.class));

		//User createUser = UserCache.getUser(waveConfig.getCreateUser());
		//User updateUser = UserCache.getUser(waveConfig.getUpdateUser());
		//waveConfigVO.setCreateUserName(createUser.getName());
		//waveConfigVO.setUpdateUserName(updateUser.getName());

		return waveConfigVO;
	}

}
