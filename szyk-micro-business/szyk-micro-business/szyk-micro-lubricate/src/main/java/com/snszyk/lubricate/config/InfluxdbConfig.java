package com.snszyk.lubricate.config;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import com.snszyk.common.config.InfluxdbProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * InfluxDB配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(InfluxdbProperties.class)
public class InfluxdbConfig {

    @Resource
    private InfluxdbProperties influxdbProperties;

    @Bean
    public InfluxDBClient influxDBClient() {
        log.info("创建InfluxDBClient，URL: {}", influxdbProperties.getUrl());
        log.info("创建InfluxDBClient，Org: {}", influxdbProperties.getOrg());
        log.info("创建InfluxDBClient，Bucket: {}", influxdbProperties.getBucket());
        
        InfluxDBClientOptions options = InfluxDBClientOptions.builder()
            .url(influxdbProperties.getUrl())
            .authenticateToken(influxdbProperties.getToken().toCharArray())
            .org(influxdbProperties.getOrg())
            .bucket(influxdbProperties.getBucket())
            .build();
            
        return InfluxDBClientFactory.create(options);
    }
}
