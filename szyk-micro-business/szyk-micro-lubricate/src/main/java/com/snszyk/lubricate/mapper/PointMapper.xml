<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.lubricate.mapper.PointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pointResultMap" type="com.snszyk.lubricate.entity.Point">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="valve_id" property="valveId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="refuel_volume" property="refuelVolume"/>
        <result column="refuel_cycle" property="refuelCycle"/>
        <result column="working_hours" property="workingHours"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="pointDTOResultMap" type="com.snszyk.lubricate.dto.PointDTO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="valve_id" property="valveId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="refuel_volume" property="refuelVolume"/>
        <result column="refuel_cycle" property="refuelCycle"/>
        <result column="working_hours" property="workingHours"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="page" resultMap="pointDTOResultMap">
        SELECT
            point.*
        FROM
            lubricate_point point
        RIGHT JOIN lubricate_scene_equipment equipment ON point.equipment_id = equipment.equipment_id
        WHERE
            point.is_deleted = 0
        <if test="point.equipmentId != null">
            and point.equipment_id = #{point.equipmentId}
        </if>
        <if test="point.valveId != null">
            and point.valve_id = #{point.valveId}
        </if>
        <if test="point.name != null and point.name != ''">
            and point.`name` like concat('%', #{point.name}, '%')
        </if>
        order by point.create_time desc
    </select>


</mapper>
