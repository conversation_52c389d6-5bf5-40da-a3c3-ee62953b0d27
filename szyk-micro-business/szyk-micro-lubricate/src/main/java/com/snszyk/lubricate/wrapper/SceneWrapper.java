/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.lubricate.entity.Scene;
import com.snszyk.lubricate.vo.SceneVO;
import com.snszyk.system.user.cache.UserCache;
import com.snszyk.system.user.entity.User;

import java.util.Objects;

/**
 * 润滑场景表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public class SceneWrapper extends BaseEntityWrapper<Scene, SceneVO> {

	public static SceneWrapper build() {
		return new SceneWrapper();
 	}

	@Override
	public SceneVO entityVO(Scene scene) {
		SceneVO sceneVO = Objects.requireNonNull(BeanUtil.copy(scene, SceneVO.class));
		User createUser = UserCache.getUser(scene.getCreateUser());
		User updateUser = UserCache.getUser(scene.getUpdateUser());
		sceneVO.setCreateUserName(createUser.getName());
		sceneVO.setUpdateUserName(updateUser.getName());
		return sceneVO;
	}

}
