package com.snszyk.sidas.smart.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Data
@Component
@Configuration
@ConfigurationProperties(prefix = "wave")
public class WaveProperties {
	/**
	 * 提取波形服务地址
	 */
    private String url;
	/**
	 * 提取波形特征服务地址
	 */
	private String featureRoute;

	private String waveRoute;

	/**
	 * 文件目录
	 */
	private String fileUrl;

}
