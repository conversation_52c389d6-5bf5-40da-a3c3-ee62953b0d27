/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.service.logic;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.*;
import com.snszyk.sidas.basic.dto.DeviceDTO;
import com.snszyk.sidas.basic.dto.MonitorDTO;
import com.snszyk.sidas.basic.dto.WaveDTO;
import com.snszyk.sidas.basic.entity.StandardDict;
import com.snszyk.sidas.basic.enums.*;
import com.snszyk.sidas.basic.feign.IBasicClient;
import com.snszyk.sidas.basic.vo.EquipmentVO;
import com.snszyk.sidas.basic.vo.SensorInstanceParamVO;
import com.snszyk.sidas.basic.vo.SensorInstanceVO;
import com.snszyk.sidas.basic.vo.StandardDictVO;
import com.snszyk.sidas.diagnosis.dto.AlarmThresholdDTO;
import com.snszyk.sidas.diagnosis.entity.AlarmThreshold;
import com.snszyk.sidas.diagnosis.enums.ThresholdAlarmTypeEnum;
import com.snszyk.sidas.diagnosis.enums.ThresholdQuotaTypeEnum;
import com.snszyk.sidas.diagnosis.enums.ThresholdTypeEnum;
import com.snszyk.sidas.diagnosis.service.IAlarmThresholdService;
import com.snszyk.sidas.diagnosis.vo.AlarmThresholdVO;
import com.snszyk.sidas.diagnosis.vo.ThresholdSelectVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.system.feign.IDictBizClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报警门限表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Slf4j
@AllArgsConstructor
@Service
public class AlarmThresholdLogicService {

	private final IAlarmThresholdService alarmThresholdService;
	private final IBasicClient basicClient;
	private final IDictBizClient dictBizClient;

	/**
	 * 振动指标门限分页列表
	 *
	 * @param page
	 * @param alarmThreshold
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.sidas.alarm.dto.AlarmThresholdDTO>
	 * <AUTHOR>
	 * @date 2023/03/16 10:56
	 */
	public IPage<AlarmThresholdDTO> page(IPage<AlarmThresholdDTO> page, AlarmThresholdVO alarmThreshold) {
		IPage<AlarmThresholdDTO> iPage;
		if(Func.isNotEmpty(alarmThreshold.getSampleDataType())){
			iPage = alarmThresholdService.stressQuotaPage(page, alarmThreshold);
		} else {
			iPage = alarmThresholdService.vibrationQuotaPage(page, alarmThreshold);
		}
		if (iPage != null && Func.isNotEmpty(iPage.getRecords())) {
			iPage.getRecords().forEach(dto -> {
				dto.setPathName(dto.getPathName().split(StringPool.COMMA + dto.getMonitorName())[0].replace(StringPool.COMMA, StringPool.SLASH));
				if(Func.isNotEmpty(dto.getSampleDataType())){
					dto.setSampleDataTypeName(SampledDataTypeEnum.getByCode(dto.getSampleDataType()).getName());
				}
				dto.setQuotaName(NonVibrationDataEnum.getByCode(dto.getQuotaType()).getName())
					.setAlarmTypeName(ThresholdAlarmTypeEnum.getByCode(dto.getAlarmType()).getName())
					.setMeasureDirectionName(DictBizCache.getValue(DictBizEnum.SENSOR_MEASURE_DIRECTION, dto.getMeasureDirection()))
					.setVibrationTypeName(VibrationTypeEnum.getByCode(dto.getVibrationType()).getName());
				if(Func.isNotEmpty(dto.getWaveId())){
					R<WaveDTO> waveDTO = basicClient.waveInfoById(dto.getWaveId());
					if(waveDTO.isSuccess() && Func.isNotEmpty(waveDTO)){
						dto.setWaveName(waveDTO.getData().getWaveName());
					}
				}
				if (dto.getHasChildren() > 0) {
					LambdaQueryWrapper<AlarmThreshold> queryWrapper = Wrappers.<AlarmThreshold>query().lambda()
						.eq(AlarmThreshold::getMonitorId, dto.getMonitorId())
						.eq(AlarmThreshold::getSampleDataType, dto.getSampleDataType()).eq(Func.isNotEmpty(dto.getStressNumber()), AlarmThreshold::getStressNumber, dto.getStressNumber());
					if(SampledDataTypeEnum.STRESS != SampledDataTypeEnum.getByCode(dto.getSampleDataType())){
						queryWrapper.eq(AlarmThreshold::getMeasureDirection, dto.getMeasureDirection());
					}
					queryWrapper.ne(AlarmThreshold::getQuotaType, dto.getQuotaType()).orderByAsc(AlarmThreshold::getId);
					List<AlarmThreshold> list = alarmThresholdService.list(queryWrapper);
					dto.setChildren(list.stream().map(threshold -> {
						AlarmThresholdDTO alarmThresholdDTO = Objects.requireNonNull(BeanUtil.copy(threshold, AlarmThresholdDTO.class));
						alarmThresholdDTO.setPathName(dto.getPathName()).setMonitorName(dto.getMonitorName())
							.setSampleDataTypeName(SampledDataTypeEnum.getByCode(dto.getSampleDataType()).getName())
							.setQuotaName(ThresholdQuotaTypeEnum.getByCode(threshold.getQuotaType()).getName())
							.setAlarmTypeName(ThresholdAlarmTypeEnum.getByCode(threshold.getAlarmType()).getName())
							.setMeasureDirectionName(DictBizCache.getValue(DictBizEnum.SENSOR_MEASURE_DIRECTION, threshold.getMeasureDirection()))
							.setVibrationTypeName(VibrationTypeEnum.getByCode(threshold.getVibrationType()).getName());
						return alarmThresholdDTO;
					}).collect(Collectors.toList()));
				}
			});
		}
		return iPage;
	}

	/**
	 * 温度指标门限分页
	 *
	 * @param page
	 * @param alarmThreshold
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.sidas.diagnosis.dto.AlarmThresholdDTO>
	 * <AUTHOR>
	 * @date 2024/01/04 14:31
	 */
	public IPage<AlarmThresholdDTO> nonVibrationQuotaPage(IPage<AlarmThresholdDTO> page, AlarmThresholdVO alarmThreshold) {
		IPage<AlarmThresholdDTO> iPage = alarmThresholdService.nonVibrationQuotaPage(page, alarmThreshold);
		if (iPage != null && Func.isNotEmpty(iPage.getRecords())) {
			iPage.getRecords().forEach(dto -> {
				dto.setPathName(dto.getPathName().substring(0, dto.getPathName().lastIndexOf(StringPool.COMMA))
					.replace(StringPool.COMMA, StringPool.SLASH))
					.setSampleDataTypeName(AlarmIndexEnum.getByCode(dto.getSampleDataType()).getName())
					.setAlarmTypeName(ThresholdAlarmTypeEnum.getByCode(dto.getAlarmType()).getName())
					.setMeasureDirectionName(DictBizCache.getValue(DictBizEnum.SENSOR_MEASURE_DIRECTION, dto.getMeasureDirection()))
					.setVibrationTypeName(VibrationTypeEnum.getByCode(dto.getVibrationType()).getName());
			});
		}
		return iPage;
	}

	/**
	 * 初始化报警门限
	 *
	 * @param dataList
	 * @return void
	 * <AUTHOR>
	 * @date 2024/1/15 18:16
	 */
	public void initAlarmThreshold(List<SensorInstanceVO> dataList) {
		Long monitorId = dataList.get(0).getMonitorId();
		R<MonitorDTO> monitorResult = basicClient.monitorInfoById(monitorId);
		if (!monitorResult.isSuccess() || Func.isEmpty(monitorResult.getData())) {
			log.info("获取测点失败！monitorId = {}", monitorId);
			return;
		}
		MonitorDTO monitorDTO = monitorResult.getData();
		dataList.stream().filter(sensorInstance ->
			SensorCategoryEnum.ELECTRIC != SensorCategoryEnum.getByCode(sensorInstance.getCategory())
			&& SensorCategoryEnum.RPM != SensorCategoryEnum.getByCode(sensorInstance.getCategory())).forEach(sensorInstance -> {
			List<SensorInstanceParamVO> sensorInstanceParamList = sensorInstance.getSensorInstanceParamList();
			// 1：温振一体，2：应力波，3：电流，4：转速
			if(SensorCategoryEnum.STRESS_WAVE == SensorCategoryEnum.getByCode(sensorInstance.getCategory())){
				sensorInstanceParamList.stream()
					// 传感器在线状态、设备运行状态不报警
					.filter(sensorInstanceParam -> SampledDataTypeEnum.SENSOR_ONLINE != SampledDataTypeEnum.getByCode(sensorInstanceParam.getSampleDataType())
						&& SampledDataTypeEnum.EQUIPMENT_RUNNING != SampledDataTypeEnum.getByCode(sensorInstanceParam.getSampleDataType()))
					.forEach(sensorInstanceParam -> {
						AlarmThreshold entity;
						if(Func.isEmpty(sensorInstanceParam.getFeature())){
							if(VibrationTypeEnum.NON_VIBRATION == VibrationTypeEnum.getByCode(sensorInstanceParam.getVibrationType())){
								// 温度、电压、传感器电量
								entity = alarmThresholdService.getOne(Wrappers.<AlarmThreshold>query().lambda()
									.eq(AlarmThreshold::getMonitorId, monitorId).eq(AlarmThreshold::getSampleDataType, sensorInstanceParam.getSampleDataType()));
							} else {
								// 应力波、振动
								LambdaQueryWrapper<AlarmThreshold> queryWrapper = Wrappers.<AlarmThreshold>query().lambda()
									.eq(AlarmThreshold::getMonitorId, monitorId).eq(AlarmThreshold::getSampleDataType, sensorInstanceParam.getSampleDataType())
									.eq(AlarmThreshold::getQuotaType, ThresholdQuotaTypeEnum.EFFECTIVE_VALUE.getCode()).eq(AlarmThreshold::getStressNumber, sensorInstance.getNumber());
								if(SampledDataTypeEnum.STRESS != SampledDataTypeEnum.getByCode(sensorInstanceParam.getSampleDataType())){
									queryWrapper.eq(AlarmThreshold::getMeasureDirection, sensorInstanceParam.getMeasureDirection());
								}
								entity = alarmThresholdService.getOne(queryWrapper);
							}
							if(Func.isEmpty(entity)){
								AlarmThresholdVO vo = new AlarmThresholdVO().toAlarmThresholdVO(monitorDTO, sensorInstanceParam);
								AlarmThreshold alarmThreshold = Objects.requireNonNull(BeanUtil.copy(vo, AlarmThreshold.class));
								alarmThreshold.setSensorCode(sensorInstance.getCode()).setStressNumber(sensorInstance.getNumber());
								// 波形id
								//R<WaveDTO> waveDTO = basicClient.waveInfoByParamId(sensorInstanceParam.getId());
								//if(waveDTO.isSuccess() && Func.isNotEmpty(waveDTO.getData())){
								//	alarmThreshold.setWaveId(waveDTO.getData().getId());
								//}
								alarmThreshold.setId(null);
								alarmThresholdService.save(alarmThreshold);
							}
						}
					});
			} else{
				sensorInstanceParamList.stream()
					// 传感器在线状态和设备运行状态不报警
					.filter(paramVO -> SampledDataTypeEnum.SENSOR_ONLINE != SampledDataTypeEnum.getByCode(paramVO.getSampleDataType())
						&& SampledDataTypeEnum.EQUIPMENT_RUNNING != SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()))
					.forEach(sensorInstanceParam -> {
						AlarmThreshold entity;
						if(Func.isEmpty(sensorInstanceParam.getFeature())){
							if(VibrationTypeEnum.NON_VIBRATION == VibrationTypeEnum.getByCode(sensorInstanceParam.getVibrationType())){
								entity = alarmThresholdService.getOne(Wrappers.<AlarmThreshold>query().lambda()
									.eq(AlarmThreshold::getMonitorId, monitorId).eq(AlarmThreshold::getSampleDataType, sensorInstanceParam.getSampleDataType()));
							} else {
								entity = alarmThresholdService.getOne(Wrappers.<AlarmThreshold>query().lambda()
									.eq(AlarmThreshold::getMonitorId, monitorId).eq(AlarmThreshold::getSampleDataType, sensorInstanceParam.getSampleDataType())
									.eq(AlarmThreshold::getQuotaType, ThresholdQuotaTypeEnum.EFFECTIVE_VALUE.getCode())
									.eq(AlarmThreshold::getMeasureDirection, sensorInstanceParam.getMeasureDirection()).isNull(AlarmThreshold::getStressNumber));
							}
							if(Func.isEmpty(entity)){
								AlarmThresholdVO vo = new AlarmThresholdVO().toAlarmThresholdVO(monitorDTO, sensorInstanceParam);
								AlarmThreshold alarmThreshold = Objects.requireNonNull(BeanUtil.copy(vo, AlarmThreshold.class));
								// 波形id
								R<WaveDTO> waveDTO = basicClient.waveInfoByParamId(sensorInstanceParam.getId());
								if(waveDTO.isSuccess() && Func.isNotEmpty(waveDTO.getData())){
									alarmThreshold.setWaveId(waveDTO.getData().getId());
								}
								alarmThreshold.setSensorCode(sensorInstance.getCode()).setId(null);
								alarmThresholdService.save(alarmThreshold);
							}
						}
					});
			}
		});
	}

	/**
	 * 删除测点同步删除报警门限
	 *
	 * @param paths
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/03/15 11:51
	 */
	public boolean removeAlarmThreshold(String paths) {
		if (Func.isNotBlank(paths)) {
			if (paths.startsWith(StringPool.LEFT_SQ_BRACKET) && paths.endsWith(StringPool.RIGHT_SQ_BRACKET)) {
				List<String> list = JSONUtil.toList(paths, String.class);
				if (Func.isNotEmpty(list)) {
					list.forEach(path -> {
						alarmThresholdService.remove(Wrappers.<AlarmThreshold>query().lambda().like(AlarmThreshold::getDevicePath, path));
					});
				}
			} else {
				alarmThresholdService.remove(Wrappers.<AlarmThreshold>query().lambda().like(AlarmThreshold::getDevicePath, paths));
			}
		}
		return true;
	}

	/**
	 * 设备功率更新同步更新报警门限
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/03/15 13:56
	 */
	public boolean updateAlarmThreshold(EquipmentVO vo) {
		return alarmThresholdService.update(Wrappers.<AlarmThreshold>update().lambda().set(AlarmThreshold::getDevicePower, vo.getPower())
			.set(AlarmThreshold::getFirstThresholdLower, null).set(AlarmThreshold::getFirstThresholdUpper, null)
			.set(AlarmThreshold::getSecondThresholdLower, null).set(AlarmThreshold::getSecondThresholdUpper, null)
			.set(AlarmThreshold::getThirdThresholdLower, null).set(AlarmThreshold::getThirdThresholdUpper, null)
			.set(AlarmThreshold::getFourthThresholdLower, null).set(AlarmThreshold::getFourthThresholdUpper, null)
			.ne(AlarmThreshold::getQuotaType, ThresholdTypeEnum.SAMPLE_VALUE).eq(AlarmThreshold::getEquipmentId, vo.getId()));
	}

	/**
	 * 门限自动铺设
	 *
	 * @param deptId
	 * @param tenantId
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/03/15 09:36
	 */
	public boolean standardAutoSet(Integer type, String deptId, String tenantId) {
		if (AuthUtil.isAdministrator()) {
			throw new ServiceException("超管无法进行当前操作！");
		}
		R<DeviceDTO> deviceR = basicClient.getDeviceByCode(deptId);
		if (deviceR.isSuccess() && Func.isNotEmpty(deviceR.getData())) {
			Long deviceId = deviceR.getData().getId();
			List<AlarmThreshold> list = alarmThresholdService.list(Wrappers.<AlarmThreshold>query().lambda()
				.isNull(AlarmThreshold::getFirstThresholdLower).isNull(AlarmThreshold::getFirstThresholdUpper)
				.isNull(AlarmThreshold::getSecondThresholdLower).isNull(AlarmThreshold::getSecondThresholdUpper)
				.isNull(AlarmThreshold::getThirdThresholdLower).isNull(AlarmThreshold::getThirdThresholdUpper)
				.isNull(AlarmThreshold::getFourthThresholdLower).isNull(AlarmThreshold::getFourthThresholdUpper)
				.eq(AlarmThreshold::getTenantId, tenantId).eq(AlarmThreshold::getType, type).isNotNull(AlarmThreshold::getQuotaType)
				.like(Func.isNotEmpty(deptId), AlarmThreshold::getDevicePath, deviceId));
			if (Func.isNotEmpty(list)) {
				List<AlarmThreshold> updateList;
				if (ThresholdTypeEnum.SAMPLE_VALUE != ThresholdTypeEnum.getByCode(type)) {
					updateList = this.autoSetting(list, type);
				} else {
					updateList = this.svAutoSetting(list);
				}
				if (Func.isNotEmpty(updateList)) {
					return alarmThresholdService.updateBatchById(updateList);
				}
			}
		}
		throw new ServiceException("未配置相应的ISO10816门限！");
	}

	/**
	 * 根据选择铺设门限
	 *
	 * @param type
	 * @param ids
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/03/15 18:26
	 */
	public boolean autoSetByIds(Integer type, List<Long> ids) {
		if (AuthUtil.isAdministrator()) {
			throw new ServiceException("超管无法进行当前操作！");
		}
		List<AlarmThreshold> list = alarmThresholdService.list(Wrappers.<AlarmThreshold>query().lambda().isNotNull(AlarmThreshold::getQuotaType)
			.isNull(AlarmThreshold::getFirstThresholdLower).isNull(AlarmThreshold::getFirstThresholdUpper)
			.isNull(AlarmThreshold::getSecondThresholdLower).isNull(AlarmThreshold::getSecondThresholdUpper)
			.isNull(AlarmThreshold::getThirdThresholdLower).isNull(AlarmThreshold::getThirdThresholdUpper)
			.isNull(AlarmThreshold::getFourthThresholdLower).isNull(AlarmThreshold::getFourthThresholdUpper)
			.in(AlarmThreshold::getId, ids));
		if (Func.isNotEmpty(list)) {
			List<AlarmThreshold> updateList;
			if (ThresholdTypeEnum.SAMPLE_VALUE != ThresholdTypeEnum.getByCode(type)) {
				updateList = this.autoSetting(list, type);
			} else {
				updateList = this.svAutoSetting(list);
			}
			if (Func.isNotEmpty(updateList)) {
				return alarmThresholdService.updateBatchById(updateList);
			}
		}
		throw new ServiceException("未配置相应的ISO10816门限！");
	}

	/**
	 * 通用指标和其他指标门限自动铺设
	 *
	 * @param list
	 * @return java.util.List<com.snszyk.sidas.alarm.entity.AlarmThreshold>
	 * <AUTHOR>
	 * @date 2023/03/15 10:57
	 */
	private List<AlarmThreshold> autoSetting(List<AlarmThreshold> list, Integer type) {
		List<AlarmThreshold> updateList = new ArrayList<>();
		R<List<StandardDictVO>> r = basicClient.standardDictByType(type);
		if (!r.isSuccess()) {
			throw new ServiceException("ISO10816门限数据获取失败！");
		}
		if (Func.isEmpty(r.getData())) {
			throw new ServiceException("未配置相应的ISO10816门限！");
		}
		Map<Integer, List<StandardDict>> tempMap = r.getData().stream().collect(Collectors.groupingBy(StandardDict::getDeviceCategory));
		list.forEach(alarmThreshold -> {
			if (tempMap.containsKey(alarmThreshold.getDeviceCategory())) {
				BigDecimal devicePower = alarmThreshold.getDevicePower();
				if (Func.isNotEmpty(devicePower)) {
					List<StandardDict> dictList = tempMap.get(alarmThreshold.getDeviceCategory());
					for (StandardDict standardDict : dictList) {
						if (devicePower.compareTo(standardDict.getPowerLower()) >= 0
							&& devicePower.compareTo(standardDict.getPowerUpper()) < 0
							&& Func.equals(alarmThreshold.getQuotaType(), standardDict.getQuotaType())
							&& Func.equals(alarmThreshold.getAlarmType(), standardDict.getAlarmType())) {
							AlarmThreshold updateThreshold = Objects.requireNonNull(BeanUtil.copy(alarmThreshold, AlarmThreshold.class));
							updateThreshold.setFirstThresholdLower(standardDict.getFirstThresholdLower()).setFirstThresholdUpper(standardDict.getFirstThresholdUpper())
								.setSecondThresholdLower(standardDict.getSecondThresholdLower()).setSecondThresholdUpper(standardDict.getSecondThresholdUpper())
								.setThirdThresholdLower(standardDict.getThirdThresholdLower()).setThirdThresholdUpper(standardDict.getThirdThresholdUpper())
								.setFourthThresholdLower(standardDict.getFourthThresholdLower()).setFourthThresholdUpper(standardDict.getFourthThresholdUpper())
								.setUpdateTime(DateUtil.now());
							updateList.add(updateThreshold);
						}
					}
				}
			}
		});
		return updateList;
	}

	/**
	 * 采样值指标门限自动铺设
	 *
	 * @param list
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/03/15 17:23
	 */
	private List<AlarmThreshold> svAutoSetting(List<AlarmThreshold> list) {
		R<List<StandardDictVO>> r = basicClient.standardDictByType(ThresholdTypeEnum.SAMPLE_VALUE.getCode());
		if (!r.isSuccess()) {
			throw new ServiceException("采样值指标数据获取失败！");
		}
		if (Func.isEmpty(r.getData())) {
			throw new ServiceException("未设置相应的采样值指标！");
		}
		List<AlarmThreshold> updateList = new ArrayList<>();
		//Map<String, List<AlarmThreshold>> tempMap = list.stream().collect(Collectors.groupingBy(AlarmThreshold::getQuotaType));
		Map<String, List<AlarmThreshold>> tempMap = list.stream().collect(Collectors.groupingBy(threshold -> threshold.getQuotaType() + threshold.getAlarmType()));
		r.getData().forEach(sd -> {
			if (Func.isNotEmpty(tempMap.get(sd.getQuotaType() + sd.getAlarmType()))) {
				tempMap.get(sd.getQuotaType() + sd.getAlarmType()).forEach(alarmThreshold -> {
					AlarmThreshold updateThreshold = Objects.requireNonNull(BeanUtil.copy(alarmThreshold, AlarmThreshold.class));
					updateThreshold.setFirstThresholdLower(sd.getFirstThresholdLower()).setFirstThresholdUpper(sd.getFirstThresholdUpper())
						.setSecondThresholdLower(sd.getSecondThresholdLower()).setSecondThresholdUpper(sd.getSecondThresholdUpper())
						.setThirdThresholdLower(sd.getThirdThresholdLower()).setThirdThresholdUpper(sd.getThirdThresholdUpper())
						.setFourthThresholdLower(sd.getFourthThresholdLower()).setFourthThresholdUpper(sd.getFourthThresholdUpper());
					updateList.add(updateThreshold);
				});
			}
		});
		return updateList;
	}

	/**
	 * 通用指标门限提交
	 *
	 * <AUTHOR>
	 * @date 2023/07/12 17:11
	 * @param vo
	 * @return boolean
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean submitGeneral(AlarmThresholdVO vo) {
		// 校验唯一性
		LambdaQueryWrapper<AlarmThreshold> queryWrapper = Wrappers.<AlarmThreshold>query().lambda().ne(Func.isNotEmpty(vo.getId()), AlarmThreshold::getId, vo.getId())
			.eq(AlarmThreshold::getMonitorId, vo.getMonitorId()).eq(AlarmThreshold::getSensorCode, vo.getSensorCode())
			.eq(AlarmThreshold::getQuotaType, vo.getQuotaType()).eq(AlarmThreshold::getAlarmType, vo.getAlarmType())
			.eq(AlarmThreshold::getVibrationType, vo.getVibrationType()).eq(AlarmThreshold::getSampleDataType, vo.getSampleDataType())
			.eq(Func.isNotEmpty(vo.getStressNumber()), AlarmThreshold::getStressNumber, vo.getStressNumber());
		if(SampledDataTypeEnum.STRESS != SampledDataTypeEnum.getByCode(vo.getSampleDataType())){
			queryWrapper.eq(AlarmThreshold::getMeasureDirection, vo.getMeasureDirection());
		}
		int count = alarmThresholdService.count(queryWrapper);
		if (count > 0) {
			R<DictBiz> dictBizR = dictBizClient.getDictValue(AuthUtil.getTenantId(), DictBizEnum.THRESHOLD_ALARM_TYPE.getName(), Func.toStr(vo.getAlarmType()));
			String alarmTypeName = "";
			if (dictBizR.isSuccess() && Func.isNotEmpty(dictBizR.getData())) {
				alarmTypeName = dictBizR.getData().getDictValue();
			}
			throw new ServiceException(StringUtil.format("当前相同[部位、波形、数据类型、测量方向、测量指标]已存在报警类型为{}的门限设置", alarmTypeName));
		}
		AlarmThreshold alarmThreshold = Objects.requireNonNull(BeanUtil.copy(vo, AlarmThreshold.class));
		if (Func.isNotEmpty(vo.getId())) {
			alarmThreshold = alarmThresholdService.getById(vo.getId());
			alarmThreshold.setQuotaType(vo.getQuotaType()).setAlarmType(vo.getAlarmType())
				.setFirstThresholdLower(vo.getFirstThresholdLower()).setFirstThresholdUpper(vo.getFirstThresholdUpper())
				.setSecondThresholdLower(vo.getSecondThresholdLower()).setSecondThresholdUpper(vo.getSecondThresholdUpper())
				.setThirdThresholdLower(vo.getThirdThresholdLower()).setThirdThresholdUpper(vo.getThirdThresholdUpper())
				.setFourthThresholdLower(vo.getFourthThresholdLower()).setFourthThresholdUpper(vo.getFourthThresholdUpper());
		}
		return alarmThresholdService.saveOrUpdate(alarmThreshold);
	}

	/**
	 * 根据波形id获取门限类型下拉列表
	 *
	 * @param waveId
	 * @return java.util.List<com.snszyk.sidas.diagnosis.vo.ThresholdSelectVO>
	 * <AUTHOR>
	 * @date 2024/2/1 9:56
	 */
	public List<ThresholdSelectVO> thresholdSelectList(Long waveId) {
		List<ThresholdAlarmTypeEnum> thresholdAlarmTypeEnums = Arrays.asList(ThresholdAlarmTypeEnum.values());
		AlarmThreshold alarmThreshold = alarmThresholdService.getOne(Wrappers.<AlarmThreshold>query().lambda()
			.eq(AlarmThreshold::getWaveId, waveId)
			.eq(AlarmThreshold::getQuotaType, ThresholdQuotaTypeEnum.EFFECTIVE_VALUE.getCode()));
		if(alarmThreshold == null){
			return thresholdAlarmTypeEnums.stream().map(thresholdAlarmTypeEnum -> {
				ThresholdSelectVO vo =
					new ThresholdSelectVO(thresholdAlarmTypeEnum.getCode(), thresholdAlarmTypeEnum.getName(), Boolean.FALSE);
				return vo;
			}).collect(Collectors.toList());
		}
		return thresholdAlarmTypeEnums.stream().map(thresholdAlarmTypeEnum -> {
			ThresholdSelectVO vo =
				new ThresholdSelectVO(thresholdAlarmTypeEnum.getCode(), thresholdAlarmTypeEnum.getName(), Boolean.FALSE);
			if(thresholdAlarmTypeEnum == ThresholdAlarmTypeEnum.getByCode(alarmThreshold.getAlarmType())){
				vo.setCanSelect(Boolean.TRUE).setThresholdId(alarmThreshold.getId());
			}
			return vo;
		}).collect(Collectors.toList());
	}

	/**
	 * 门户波形报警门限修改
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/2/1 10:16
	 */
	public boolean updateThreshold(AlarmThresholdVO vo) {
		AlarmThreshold alarmThreshold = alarmThresholdService.getById(vo.getId());
		if(alarmThreshold == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		alarmThreshold.setFirstThresholdLower(vo.getFirstThresholdLower()).setFirstThresholdUpper(vo.getFirstThresholdUpper())
			.setSecondThresholdLower(vo.getSecondThresholdLower()).setSecondThresholdUpper(vo.getSecondThresholdUpper())
			.setThirdThresholdLower(vo.getThirdThresholdLower()).setThirdThresholdUpper(vo.getThirdThresholdUpper())
			.setFourthThresholdLower(vo.getFourthThresholdLower()).setFourthThresholdUpper(vo.getFourthThresholdUpper());
        return alarmThresholdService.updateById(alarmThreshold);
	}

}
