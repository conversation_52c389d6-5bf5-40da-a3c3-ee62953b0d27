package com.snszyk.sidas.diagnosis.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.sidas.basic.dto.DeviceDTO;
import com.snszyk.sidas.basic.feign.IBasicClient;
import com.snszyk.sidas.diagnosis.dto.AlarmDeviceDetailDTO;
import com.snszyk.sidas.diagnosis.dto.AlarmTrendDTO;
import com.snszyk.sidas.diagnosis.entity.Alarm;
import com.snszyk.sidas.diagnosis.entity.AlarmRecord;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.system.feign.ISysClient;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 诊断模块门户端大屏接口服务类
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DashboardService {

	private final ISysClient sysClient;
	private final IBasicClient basicClient;
	private final IAlarmService alarmService;
	private final IAlarmRecordService alarmRecordService;

	/**
	 * 集团驾驶舱 - 厂区设备报警状态分布
	 * @param tenantId 租户ID
	 * @param deptId 部门ID
	 * @return 设备报警状态分布
	 */
	public R<List<Integer>> groupAlarmStateDistribution(String tenantId, String deptId) {
		R<DeviceDTO> deviceR = basicClient.getDeviceByCode(getGroupCockpitDeptId(deptId));
		if (!deviceR.isSuccess() || Func.isEmpty(deviceR.getData())) {
			return R.data(Collections.emptyList());
		}

		List<Integer> stateDistribution = sidasStateDistribution(tenantId, Func.toStr(deviceR.getData().getId()));
		if (CollectionUtil.isNotEmpty(stateDistribution)) {
			R<Integer> equipmentAmountR = basicClient.equipmentAmount(tenantId, Func.toStr(deviceR.getData().getId()));
			if (equipmentAmountR.isSuccess()) {
				Integer normal = equipmentAmountR.getData();
				if (Func.isNotEmpty(stateDistribution)) {
					Integer alarmSum = stateDistribution.stream().reduce(Integer::sum).orElse(0);
					normal = normal - alarmSum;
				}
				if (normal < 0) {
					normal = 0;
				}
				stateDistribution.add(0, normal);
				return R.data(stateDistribution);
			}
		}
		return R.fail("获取数据失败");
	}

	/**
	 * 获取设备报警状态分布
	 * @param tenantId 租户
	 * @param devicePath 设备路径
	 * @return
	 */
	private List<Integer> sidasStateDistribution(String tenantId, String devicePath) {
		List<Integer> list = new ArrayList<>();
		List<DictBiz> dictBizList = DictBizCache.getList(DictBizEnum.ALARM_LEVEL.getName());
		if (Func.isEmpty(dictBizList)) {
			return null;
		}
		List<String> keyList = dictBizList.stream()
			.map(DictBiz::getDictKey)
			.sorted()
			.distinct()
			.collect(Collectors.toList());
		for (String key : keyList) {
			LambdaQueryWrapper<Alarm> queryWrapper = Wrappers.lambdaQuery();
			queryWrapper.eq(Alarm::getAlarmLevel, key);
			queryWrapper.like(Alarm::getDevicePath, devicePath)
				.ne(Alarm::getStatus, 2);
			int count = alarmService.count(queryWrapper);
			list.add(count);
		}
		return list;
	}

	/**
	 * 集团驾驶舱 - 厂区设备报警概况
	 * @param tenantId 租户ID
	 * @param deptId 部门ID
	 * @return 厂区设备报警概况
	 */
	public R<AlarmDeviceDetailDTO> groupSidasAlarmInfo(String tenantId, String deptId) {
		List<String> names = new ArrayList<>();
		List<Integer> normals = new ArrayList<>();
		List<Integer> exceptions = new ArrayList<>();

		R<DeviceDTO> deviceR = basicClient.getDeviceByCode(getGroupCockpitDeptId(deptId));
		if (deviceR.isSuccess() && Func.isNotEmpty(deviceR.getData())) {
			R<List<DeviceDTO>> deviceChildR = basicClient.getDeviceChild(deviceR.getData().getId());
			if (deviceChildR.isSuccess() && Func.isNotEmpty(deviceChildR.getData())) {
				for (DeviceDTO device : deviceChildR.getData()) {
					//全部设备
					R<Integer> equipmentAmountR = basicClient.equipmentAmount(tenantId, Func.toStr(device.getId()));
					if (!equipmentAmountR.isSuccess()) {
						return R.fail("获取数据失败");
					}
					if (equipmentAmountR.getData() == null || equipmentAmountR.getData() == 0) {
						continue;
					}

					//异常统计
					LambdaQueryWrapper<Alarm> queryWrapper = Wrappers.lambdaQuery();
					queryWrapper.like(Alarm::getDevicePath, device.getPath())
						.in(Alarm::getStatus, Arrays.asList("0", "1"));
					int alarmCount = alarmService.count(queryWrapper);
					int normal = equipmentAmountR.getData() - alarmCount;
					if (normal < 0) {
						normal = 0;
					}
					names.add(device.getName());
					normals.add(normal);
					exceptions.add(alarmCount);
				}
			}
		}
		return R.data(new AlarmDeviceDetailDTO(names, normals, exceptions));
	}

	/**
	 * 集团驾驶舱 - 最新待处理报警
	 * @param tenantId 租户
	 * @param deptId 部门id
	 * @return
	 */
	public R<List<Alarm>> groupLatestPendingAlarm(String tenantId, String deptId) {
		R<DeviceDTO> deviceR = basicClient.getDeviceByCode(getGroupCockpitDeptId(deptId));
		if (!deviceR.isSuccess() || Func.isEmpty(deviceR.getData())) {
			return R.data(Collections.emptyList());
		}

		IPage<Alarm> page = alarmService.page(Condition.getPage(new Query()),
			Wrappers.<Alarm>lambdaQuery()
				.like(Func.isNotEmpty(deviceR.getData().getPath()), Alarm::getDevicePath, deviceR.getData().getPath())
				.isNull(Alarm::getFaultId)
				.ne(Alarm::getStatus, 2)
				.orderByDesc(Alarm::getCreateTime));

		page.getRecords().forEach(alarm -> alarm.setPathName(alarm.getPathName()
				.substring(alarm.getPathName().indexOf(StringPool.COMMA) + 1)
				.replaceAll(StringPool.COMMA, StringPool.SLASH))
			.setAlarmLevelName(DictBizCache.getValue(DictBizEnum.ALARM_LEVEL, alarm.getAlarmLevel())));

		return R.data(page.getRecords());
	}

	private String getGroupCockpitDeptId(String deptId) {
		R<Dept> deptR = sysClient.getDept(Func.toLong(deptId));
		if (!deptR.isSuccess() || Func.isEmpty(deptR.getData())) {
			throw new ServiceException(ResultCode.UN_AUTHORIZED);
		}
		Dept dept = deptR.getData();
		String ancestors = dept.getAncestors();
		String[] ids = Func.split(ancestors, StringPool.COMMA);
		int length = ids.length;
		if (length > 1) {
			return ids[1];
		}
		return deptId;
	}

	/**
	 * 厂区驾驶舱 - 设备报警状态分布
	 * @param id 地点ID
	 * @return 设备报警状态分布
	 */
	public R<List<Integer>> plantAlarmStateDistribution(String id) {
		R<DeviceDTO> deviceR = basicClient.deviceInfoById(Func.toLong(id));
		if (!deviceR.isSuccess() || Func.isEmpty(deviceR.getData())) {
			return R.data(Collections.emptyList());
		}

		//获取报警状态分布
		List<Integer> distributionList = sidasStateDistribution(deviceR.getData().getTenantId(),
			Func.toStr(deviceR.getData().getId()));
		if (Func.isNotEmpty(distributionList)) {
			R<Integer> equipmentAmountR = basicClient.equipmentAmount(deviceR.getData().getTenantId(),
				Func.toStr(deviceR.getData().getId()));
			if (equipmentAmountR.isSuccess()) {
				Integer normal = equipmentAmountR.getData();
				Integer alarmSum = distributionList.stream().reduce(Integer::sum).orElse(0);
				normal = normal - alarmSum;
				if (normal < 0) {
					normal = 0;
				}
				distributionList.add(0, normal);
				return R.data(distributionList);
			}
		}
		return R.fail("获取数据失败");
	}

	/**
	 * 厂区驾驶舱 - 最新待处理报警
	 * @param id 地点ID
	 * @return 最新待处理报警
	 */
	public R<List<Alarm>> plantLatestPendingAlarm(String id) {
		R<DeviceDTO> deviceR = basicClient.deviceInfoById(Func.toLong(id));
		if (!deviceR.isSuccess() || Func.isEmpty(deviceR.getData())) {
			return R.data(Collections.emptyList());
		}

		//获取待处理报警
		IPage<Alarm> alarmPage = alarmService.page(Condition.getPage(new Query()),
			Wrappers.<Alarm>lambdaQuery()
				.like(Func.isNotEmpty(deviceR.getData().getPath()), Alarm::getDevicePath, deviceR.getData().getPath())
				.isNull(Alarm::getFaultId)
				.ne(Alarm::getStatus, 2)
				.orderByDesc(Alarm::getCreateTime));

		//路径名称更新
		alarmPage.getRecords().forEach(alarm -> alarm.setPathName(alarm.getPathName()
				.substring(alarm.getPathName().indexOf(StringPool.COMMA) + 1)
				.replaceAll(StringPool.COMMA, StringPool.SLASH))
			.setAlarmLevelName(DictBizCache.getValue(DictBizEnum.ALARM_LEVEL, alarm.getAlarmLevel())));

		return R.data(alarmPage.getRecords());
	}

	/**
	 * 报警趋势 - 按天展示
	 * @param lastDays 最近几天
	 * @return 报警趋势
	 */
	public AlarmTrendDTO getAlarmTrend(Integer lastDays) {
		DateTime startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -lastDays + 1));
		List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>lambdaQuery()
			.ge(AlarmRecord::getAlarmTime, startDate));

		if (CollectionUtil.isNotEmpty(alarmRecordList)) {
			AlarmTrendDTO result = new AlarmTrendDTO()
				.setDateList(new ArrayList<>())
				.setAlarmCountList(new ArrayList<>())
				.setEquipmentCountList(new ArrayList<>());

			//统计每天的日期、报警数、设备数
			for (int i = 0; i < lastDays; i++) {
				String date = DateUtil.format(DateUtil.offsetDay(startDate, i), "yyyy-MM-dd");
				//添加日期
				result.getDateList().add(date);

				//添加当日的报警数
				result.getAlarmCountList().add((int) alarmRecordList.stream()
					.filter(alarm -> date.equals(DateUtil.format(alarm.getAlarmTime(), "yyyy-MM-dd")))
					.count());

				//添加当日的报警设备数
				result.getEquipmentCountList().add((int) alarmRecordList.stream()
					.filter(alarm -> date.equals(DateUtil.format(alarm.getAlarmTime(), "yyyy-MM-dd")))
					.map(AlarmRecord::getEquipmentId)
					.distinct()
					.count());
			}

			return result;
		} else {
			return null;
		}
	}

	/**
	 * 最近一年报警趋势 - 按月展示
	 * @return 最近一年报警趋势
	 */
	public AlarmTrendDTO getLastYearAlarmTrend() {
		DateTime startDate = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -12 + 1));
		List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>lambdaQuery()
			.ge(AlarmRecord::getAlarmTime, startDate));

		if (CollectionUtil.isNotEmpty(alarmRecordList)) {
			AlarmTrendDTO result = new AlarmTrendDTO()
				.setDateList(new ArrayList<>())
				.setAlarmCountList(new ArrayList<>())
				.setEquipmentCountList(new ArrayList<>());

			//统计每天的日期、报警数、设备数
			for (int i = 0; i < 12; i++) {
				String date = DateUtil.format(DateUtil.offsetMonth(startDate, i), "yyyy-MM");
				//添加日期
				result.getDateList().add(date);

				//添加当月的报警数
				result.getAlarmCountList().add((int) alarmRecordList.stream()
					.filter(alarmRecord -> date.equals(DateUtil.format(alarmRecord.getAlarmTime(), "yyyy-MM")))
					.count());

				//添加当月的报警设备数
				result.getEquipmentCountList().add((int) alarmRecordList.stream()
					.filter(alarmRecord -> date.equals(DateUtil.format(alarmRecord.getAlarmTime(), "yyyy-MM")))
					.map(AlarmRecord::getEquipmentId)
					.distinct()
					.count());
			}

			return result;
		} else {
			return null;
		}
	}

}
