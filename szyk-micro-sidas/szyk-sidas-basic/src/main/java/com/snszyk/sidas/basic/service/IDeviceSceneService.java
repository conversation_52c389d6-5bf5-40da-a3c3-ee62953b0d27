/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.basic.dto.DeviceSceneDTO;
import com.snszyk.sidas.basic.entity.DeviceScene;
import com.snszyk.sidas.basic.vo.DeviceSceneVO;
import com.snszyk.system.vo.DelResultVO;

import java.util.List;

/**
 * 3D场景表 服务类
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
public interface IDeviceSceneService extends BaseService<DeviceScene> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceScene
	 * @return
	 */
	IPage<DeviceSceneDTO> selectDeviceScenePage(IPage<DeviceSceneDTO> page, DeviceSceneVO deviceScene);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	DeviceSceneDTO fetchById(Long id);

	/**
	 * 提交
	 *
	 * @param deviceScene
	 * @return
	 */
	boolean submit(DeviceSceneVO deviceScene);

	/**
	 * 逻辑删除
	 *
	 * @param ids
	 * @return
	 */
	boolean removeByIds(List<Long> ids);

	/**
	 * 校验并删除场景
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveScene(List<Long> ids);

}
