/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.serialize.JSONObjectSerializer;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.*;
import com.snszyk.sidas.basic.dto.DeviceModelConfigDTO;
import com.snszyk.sidas.basic.dto.RealTimeDataDTO;
import com.snszyk.sidas.basic.dto.SensorModelConfigDTO;
import com.snszyk.sidas.basic.dto.SensorModelDataDTO;
import com.snszyk.sidas.basic.entity.*;
import com.snszyk.sidas.basic.enums.*;
import com.snszyk.sidas.basic.mapper.DeviceModelConfigMapper;
import com.snszyk.sidas.basic.service.*;
import com.snszyk.sidas.basic.service.logic.WaveFormLogicService;
import com.snszyk.sidas.basic.vo.CopyConfigVO;
import com.snszyk.sidas.basic.vo.DeviceModelConfigVO;
import com.snszyk.sidas.basic.vo.SensorModelConfigVO;
import com.snszyk.sidas.basic.vo.SensorMonitorConfigVO;
import com.snszyk.sidas.basic.wrapper.DeviceModelConfigWrapper;
import com.snszyk.sidas.diagnosis.enums.AlarmLevelEnum;
import com.snszyk.sidas.diagnosis.enums.ThresholdAlarmTypeEnum;
import com.snszyk.sidas.diagnosis.feign.IAlarmClient;
import com.snszyk.sidas.diagnosis.vo.AlarmRecordVO;
import com.snszyk.sidas.diagnosis.vo.AlarmThresholdVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.enums.DictBizEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 3D模型配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Slf4j
@Service
@AllArgsConstructor
public class DeviceModelConfigServiceImpl extends ServiceImpl<DeviceModelConfigMapper, DeviceModelConfig> implements IDeviceModelConfigService {

	private final ISensorTypeService sensorTypeService;
	private final ISensorInstanceService sensorInstanceService;
	private final ISensorInstanceParamService sensorInstanceParamService;
	private final IDisplayPositionService displayPositionService;
	private final IEquipmentService equipmentService;
	private final IMonitorService monitorService;
	private final IWaveService waveService;
	private final IAlarmClient alarmClient;
	private final SzykRedis szykRedis;
	private final WaveFormLogicService waveFormLogicService;


	@Override
	public boolean submit(SensorModelConfigVO sensorModelConfig) {
		List<SensorMonitorConfigVO> sensorMonitorConfigList = sensorModelConfig.getSensorMonitorConfigList();
		if (Func.isNotEmpty(sensorMonitorConfigList)) {
			baseMapper.removeByEquipment(sensorModelConfig.getEquipmentId(), sensorModelConfig.getCategory());
			List<DeviceModelConfig> deviceModelConfigs = new ArrayList<>();
			sensorMonitorConfigList.forEach(sensorMonitorConfig -> {
				//baseMapper.removeBySensorCode(sensorMonitorConfig.getSensorCode(), sensorModelConfig.getCategory());
				List<DeviceModelConfigVO> deviceModelConfigList = sensorMonitorConfig.getDeviceModelConfigList();
				List<DeviceModelConfig> list = deviceModelConfigList.stream().map(vo -> {
					DeviceModelConfig deviceModelConfig = Objects.requireNonNull(BeanUtil.copy(sensorMonitorConfig, DeviceModelConfig.class));
					deviceModelConfig.setEquipmentId(sensorModelConfig.getEquipmentId()).setSensorParamId(vo.getSensorParamId())
						.setSensorParamName(vo.getSensorParamName()).setSampleDataType(vo.getSampleDataType()).setQuota(JSONUtil.toJsonStr(vo.getQuotaList()))
						.setCreateUser(AuthUtil.getUserId()).setCreateTime(DateUtil.now()).setCategory(sensorModelConfig.getCategory());
					return deviceModelConfig;
				}).collect(Collectors.toList());
				deviceModelConfigs.addAll(list);
			});
			return this.saveBatch(deviceModelConfigs);
		} else {
			return this.remove(Wrappers.<DeviceModelConfig>query().lambda().eq(DeviceModelConfig::getEquipmentId, sensorModelConfig.getEquipmentId()));
		}
	}

	@Override
	public SensorModelConfigVO detail(Long equipmentId, Integer category) {
		List<DeviceModelConfig> deviceModelConfigList = this.list(Wrappers.<DeviceModelConfig>query().lambda()
			.eq(DeviceModelConfig::getEquipmentId, equipmentId).eq(DeviceModelConfig::getCategory, category).orderByAsc(DeviceModelConfig::getCreateTime));
		SensorModelConfigVO detail = null;
		if (Func.isNotEmpty(deviceModelConfigList)) {
			detail = new SensorModelConfigVO(equipmentId);
			Map<String, List<DeviceModelConfig>> configMap = deviceModelConfigList.stream().collect(Collectors.groupingBy(deviceModelConfig ->
				deviceModelConfig.getMonitorId() + StringPool.COLON + deviceModelConfig.getSensorCode()));
			List<SensorMonitorConfigVO> sensorMonitorConfigs = configMap.keySet().stream().map(key -> {
				Long monitorId = Func.toLong(key.split(StringPool.COLON)[0]);
				String sensorCode = key.split(StringPool.COLON)[1];
				SensorMonitorConfigVO vo = new SensorMonitorConfigVO(monitorId);
				List<DeviceModelConfig> list = configMap.get(key);
				vo.setSensorCode(sensorCode).setDeviceModelConfigList(DeviceModelConfigWrapper.build().listVO(list));
				// 3D模型传感器id
				SensorInstance sensorInstance = sensorInstanceService.getOne(Wrappers.<SensorInstance>query().lambda()
					.eq(SensorInstance::getCode, sensorCode).eq(SensorInstance::getIsDeleted, 0));
				vo.setVirtualSensorId(sensorInstance.getVirtualSensorId());
				return vo;
			}).collect(Collectors.toList());
			detail.setSensorMonitorConfigList(sensorMonitorConfigs);
		}
		return detail;
	}

	@Override
	public DeviceModelConfigDTO realTimeData(Long equipmentId, Integer category) {
		List<DeviceModelConfig> deviceModelConfigList = this.list(Wrappers.<DeviceModelConfig>query().lambda()
			.eq(DeviceModelConfig::getEquipmentId, equipmentId).eq(DeviceModelConfig::getCategory, category));
		Map<String, List<DeviceModelConfig>> configMap = deviceModelConfigList.stream().collect(Collectors.groupingBy(DeviceModelConfig::getSensorCode));
		List<SensorModelConfigDTO> sensorModelConfigList = configMap.keySet().stream().map(sensorCode -> {

			// 最大报警等级
			final Integer[] maxAlarmLevel = {AlarmLevelEnum.NORMAL.getCode()};
			// 3D模型传感器id
			SensorInstance sensorInstance = sensorInstanceService.getOne(Wrappers.<SensorInstance>query().lambda()
						.eq(SensorInstance::getCode, sensorCode));
			SensorType sensorType = sensorTypeService.getById(sensorInstance.getTypeId());
			Monitor monitor = monitorService.getById(sensorInstance.getMonitorId());
			SensorModelConfigDTO sensorModelConfigDTO = new SensorModelConfigDTO(sensorCode, sensorInstance.getVirtualSensorId());
			sensorModelConfigDTO.setMonitorName(monitor.getName()).setMonitorPath(monitor.getPath()).setSensorName(sensorType.getName()).setSensorModel(sensorType.getModel());
			// 2D图数据展示框位置
			if (DimensionCategoryEnum.TWO_DIMENSION == DimensionCategoryEnum.getByCode(category)) {
				DisplayPosition displayPosition = displayPositionService.getOne(Wrappers.<DisplayPosition>query().lambda()
					.eq(DisplayPosition::getEquipmentId, equipmentId).eq(DisplayPosition::getSensorCode, sensorCode)
					.eq(DisplayPosition::getCategory, category));
				if (Func.isNotEmpty(displayPosition)) {
					sensorModelConfigDTO.setDisplayPosition(displayPosition.getPosition());
				}
			}
			List<DeviceModelConfig> list = configMap.get(sensorCode);
			log.info("2D/3D实时数据展示===================：{}", configMap);


			List<RealTimeDataDTO> realTimeDataList = list.stream().map(deviceModelConfig ->
				this.assembleData(deviceModelConfig, maxAlarmLevel)).collect(Collectors.toList());
			sensorModelConfigDTO.setRealTimeDataList(realTimeDataList).setMaxAlarmLevel(maxAlarmLevel[0]);
			return sensorModelConfigDTO;
		}).collect(Collectors.toList());
		return new DeviceModelConfigDTO(sensorModelConfigList);
	}

	/**
	 * 组装实时数据
	 *
	 * @param deviceModelConfig
	 * @param maxAlarmLevel
	 * @return com.snszyk.sidas.basic.dto.RealTimeDataDTO
	 * <AUTHOR>
	 * @date 2024/3/12 15:31
	 */
	private RealTimeDataDTO assembleData(DeviceModelConfig deviceModelConfig, Integer[] maxAlarmLevel){
		RealTimeDataDTO realTimeDataDTO = Objects.requireNonNull(BeanUtil.copy(deviceModelConfig, RealTimeDataDTO.class));
		boolean flag = Arrays.stream(RealNonVibrationDataEnum.values()).anyMatch(t ->
			Func.equals(t.getCode(), deviceModelConfig.getSampleDataType()));
		AtomicReference<BigDecimal> value = new AtomicReference<>(BigDecimal.ZERO);
		//如果缓存中没有就从influxdb中查询 2025/04/16 张洪毓
		//=====开始
		/*String sampleDataType = deviceModelConfig.getSampleDataType();
		//只有温度和振动才查询infludb
		com.alibaba.fastjson.JSONObject dataJson = new com.alibaba.fastjson.JSONObject();
		if(sampleDataType.equals(SampledDataTypeEnum.ACCELERATION.getCode()) ||
			sampleDataType.equals(SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode())){
			Object o = szykRedis.get(deviceModelConfig.getSensorCode() + StringPool.COLON
				+ deviceModelConfig.getSensorParamId() + StringPool.COLON + sampleDataType);
			//如果是空的话，则查询influxdb
			if (Func.isEmpty(o)){
				dataJson = waveFormLogicService.queryLatestData(deviceModelConfig.getSensorCode(), deviceModelConfig.getSampleDataType());
			}
		}*/

		// 振动数据
		if(!flag){
			List<String> quotaList = JSONUtil.toList(deviceModelConfig.getQuota(), String.class);
			//com.alibaba.fastjson.JSONObject finalDataJson = dataJson;
			List<SensorModelDataDTO> dataList = quotaList.stream().map(quota -> {
				SensorModelDataDTO sensorModelData = new SensorModelDataDTO(quota);
				sensorModelData.setModelCodeName(NonVibrationDataEnum.getByCode(quota).getName());
				log.info("=======================获取到的key：{}", deviceModelConfig.getSensorCode() + StringPool.COLON
					+ deviceModelConfig.getSensorParamId() + StringPool.COLON + quota);
				if(Func.isNotEmpty((Object)szykRedis.get(deviceModelConfig.getSensorCode() + StringPool.COLON
					+ deviceModelConfig.getSensorParamId() + StringPool.COLON + quota))){
					value.set(new BigDecimal(szykRedis.get(deviceModelConfig.getSensorCode() + StringPool.COLON
						+ deviceModelConfig.getSensorParamId() + StringPool.COLON + quota).toString()));
				}/*else{
					// 如果缓存中没有就从influxdb中查询 2025/04/16 张洪毓
					BigDecimal data = queryQuotaData(finalDataJson, quota);
					value.set(data);
				}*/
				String unit = "";
				if(NonVibrationDataEnum.EFFECTIVE_VALUE == NonVibrationDataEnum.getByCode(quota)
					|| NonVibrationDataEnum.PEAK_VALUE == NonVibrationDataEnum.getByCode(quota)
					|| NonVibrationDataEnum.PEAK_PEAK_VALUE == NonVibrationDataEnum.getByCode(quota)){
					DictBizCache.getValue(DictBizEnum.SAMPLED_DATA_UNIT, SampledDataUnitEnum.VELOCITY.getValue());
					unit = DictBizCache.getValue(DictBizEnum.SAMPLED_DATA_UNIT, SampledDataUnitEnum.getByCode(deviceModelConfig.getSampleDataType()).getValue());
				}
				sensorModelData.setValue(value.get()).setUnit(unit);
				Map<String, List<BigDecimal>> map = this.getAlarmLevel(deviceModelConfig, quota);
				if(Func.isNotEmpty(map)){
					for(String key : map.keySet()){
						sensorModelData.setAlarmType(Func.toInt(key.split(StringPool.COLON)[0]))
							.setAlarmLevel(Func.toInt(key.split(StringPool.COLON)[1]));
						sensorModelData.setAlarmThreshold(map.get(key));
					}
				} else {
					sensorModelData.setAlarmLevel(AlarmLevelEnum.NORMAL.getCode());
				}
				// 有效值带波形id用于跳转
				if (NonVibrationDataEnum.EFFECTIVE_VALUE == NonVibrationDataEnum.getByCode(quota)) {
					Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
						.eq(Wave::getSensorCode, deviceModelConfig.getSensorCode())
						.eq(Wave::getUnbind, 0)
						.eq(Wave::getSensorInstanceParamId, deviceModelConfig.getSensorParamId()));
					SensorInstanceParam sensorInstanceParam = sensorInstanceParamService.getById(deviceModelConfig.getSensorParamId());
					if (Func.isNotEmpty(wave)) {
						sensorModelData.setWaveId(wave.getId()).setMonitorId(wave.getMonitorId());
						sensorModelData.setSamplingFreq(BigDecimal.valueOf(1000L)
								.multiply(sensorInstanceParam.getSamplingFreq() != null ? sensorInstanceParam.getSamplingFreq() : BigDecimal.ZERO))
							.setSamplingPoints(sensorInstanceParam.getSamplingPoints());
					}
				}
				if(sensorModelData.getAlarmLevel() > maxAlarmLevel[0]){
					maxAlarmLevel[0] = sensorModelData.getAlarmLevel();
				}
				return sensorModelData;
			}).collect(Collectors.toList());
			realTimeDataDTO.setDataList(dataList);
		} else {
			// 非振动数据
			SensorModelDataDTO sensorModelData = new SensorModelDataDTO(deviceModelConfig.getSampleDataType());
			sensorModelData.setModelCodeName(SampledDataTypeEnum.getByCode(deviceModelConfig.getSampleDataType()).getName());
			log.info("=======================获取到的key：{}", deviceModelConfig.getSensorCode() + StringPool.COLON
				+ deviceModelConfig.getSensorParamId() + StringPool.COLON + deviceModelConfig.getSampleDataType());
			if(Func.isNotEmpty((Object)szykRedis.get(deviceModelConfig.getSensorCode() + StringPool.COLON
				+ deviceModelConfig.getSensorParamId() + StringPool.COLON + deviceModelConfig.getSampleDataType()))){
				value.set(BigDecimal.valueOf(Func.toDouble(szykRedis.get(deviceModelConfig.getSensorCode() + StringPool.COLON
					+ deviceModelConfig.getSensorParamId() + StringPool.COLON + deviceModelConfig.getSampleDataType()))));
			}/*else {
				// 如果缓存中没有就从influxdb中查询 2025/04/16 张洪毓
				if(Func.isNotEmpty(dataJson)){
					//如果不为空则组装数据
					value.set(new BigDecimal(dataJson.getString("value")));
				}
			}*/
			String unit = DictBizCache.getValue(DictBizEnum.SAMPLED_DATA_UNIT, SampledDataUnitEnum.getByCode(deviceModelConfig.getSampleDataType()).getValue());
			sensorModelData.setValue(value.get()).setUnit(unit);
			Map<String, List<BigDecimal>> map = this.getAlarmLevel(deviceModelConfig, null);
			if(Func.isNotEmpty(map)){
				for(String key : map.keySet()){
					sensorModelData.setAlarmType(Func.toInt(key.split(StringPool.COLON)[0]))
						.setAlarmLevel(Func.toInt(key.split(StringPool.COLON)[1]));
					sensorModelData.setAlarmThreshold(map.get(key));
				}
			} else {
				sensorModelData.setAlarmLevel(AlarmLevelEnum.NORMAL.getCode());
			}
			// 温度、转速、电流有波形id用于跳转
			if (SampledDataTypeEnum.EQUIPMENT_TEMPERATURE == SampledDataTypeEnum.getByCode(deviceModelConfig.getSampleDataType())
				&& SampledDataTypeEnum.RPM == SampledDataTypeEnum.getByCode(deviceModelConfig.getSampleDataType())
				&& SampledDataTypeEnum.ELECTRIC == SampledDataTypeEnum.getByCode(deviceModelConfig.getSampleDataType())) {
				Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
					.eq(Wave::getSensorCode, deviceModelConfig.getSensorCode())
					.eq(Wave::getUnbind, 0)
					.eq(Wave::getSensorInstanceParamId, deviceModelConfig.getSensorParamId()));
				if (Func.isNotEmpty(wave)) {
					sensorModelData.setWaveId(wave.getId()).setMonitorId(wave.getMonitorId());
				}
			}
			// 设备运行状态
			if(SampledDataTypeEnum.EQUIPMENT_RUNNING == SampledDataTypeEnum.getByCode(deviceModelConfig.getSampleDataType())){
				sensorModelData.setName(DictBizCache.getValue(DictBizEnum.RUN_STATUS, sensorModelData.getValue().intValue()));
			}
			// 传感器在线状态
			if(SampledDataTypeEnum.SENSOR_ONLINE == SampledDataTypeEnum.getByCode(deviceModelConfig.getSampleDataType())){
				sensorModelData.setName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, sensorModelData.getValue().intValue()));
			}
			if(sensorModelData.getAlarmLevel() > maxAlarmLevel[0]){
				maxAlarmLevel[0] = sensorModelData.getAlarmLevel();
			}
			realTimeDataDTO.setDataList(Arrays.asList(sensorModelData));
		}
		return realTimeDataDTO;
	}

	private BigDecimal queryQuotaData(com.alibaba.fastjson.JSONObject json, String quota) {
		BigDecimal value = BigDecimal.ZERO;
		if(Func.isNotEmpty(json)){
			//如果不为空则组装数据
			//这里一一对应吧
			Object data = null;
			switch (quota){
				case "MM000":
					value = new BigDecimal(json.getString("value"));
					break;
				case "MMN01":
					data = json.get("peakValue");
					if(Func.isNotEmpty(data)){
						value = new BigDecimal(String.valueOf(data));
					}
					break;
				case "MMN02":
					data = json.get("peakPeakValue");
					if(Func.isNotEmpty(data)){
						value = new BigDecimal(String.valueOf(data));
					}
					break;
				case "MMN03":
					data = json.get("clearanceValue");
					if(Func.isNotEmpty(data)){
						value = new BigDecimal(String.valueOf(data));
					}
					break;
				case "MMN04":
					data = json.get("skewnessValue");
					if(Func.isNotEmpty(data)){
						value = new BigDecimal(String.valueOf(data));
					}
					break;
				case "MMN05":
					data = json.get("kurtosisValue");
					if(Func.isNotEmpty(data)){
						value = new BigDecimal(String.valueOf(data));
					}
					break;
				default:
					value = BigDecimal.ZERO;
					break;
			}

		}
		return  value;
	}

	/**
	 * 获取报警等级和比对门限
	 *
	 * @param deviceModelConfig
	 * @return java.lang.Integer
	 * <AUTHOR>
	 * @date 2024/3/6 17:28
	 */
	private Map<String, List<BigDecimal>> getAlarmLevel(DeviceModelConfig deviceModelConfig, String quota){
		Map<String, List<BigDecimal>> map = new HashMap<>(16);
		// 报警等级
		Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
			.eq(Wave::getSensorCode, deviceModelConfig.getSensorCode()).eq(Wave::getSampleDataType, deviceModelConfig.getSampleDataType())
			.eq(Wave::getSensorInstanceParamId, deviceModelConfig.getSensorParamId()).eq(Wave::getUnbind, 0));
		if(Func.isNotEmpty(wave)){
			R<AlarmRecordVO> alarmRecord = alarmClient.alarmRecordByWave(wave.getId(), quota);
			if(alarmRecord.isSuccess()){
				if(Func.isNotEmpty(alarmRecord.getData())){
					Integer alarmLevel = alarmRecord.getData().getAlarmLevel();
					AlarmThresholdVO vo = new AlarmThresholdVO().toAlarmThresholdVO(deviceModelConfig);
					vo.setQuotaType(quota).setMeasureDirection(wave.getMeasureDirection()).setStressNumber(wave.getNumber());
					R<AlarmThresholdVO> alarmThreshold = alarmClient.alarmThreshold(vo);
					List<BigDecimal> list = new ArrayList<>();
					switch (ThresholdAlarmTypeEnum.getByCode(alarmThreshold.getData().getAlarmType())) {
						case HORIZONTAL_OVERRUN:
							if(AlarmLevelEnum.LEVEL_ONE == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getFirstThresholdUpper());
							}
							if(AlarmLevelEnum.LEVEL_TWO == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getSecondThresholdUpper());
							}
							if(AlarmLevelEnum.LEVEL_THREE == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getThirdThresholdUpper());
							}
							if(AlarmLevelEnum.LEVEL_FOUR == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getFourthThresholdUpper());
							}
							map.put(alarmThreshold.getData().getAlarmType() + StringPool.COLON
								+ alarmRecord.getData().getAlarmLevel(), list);
							break;
						case HORIZONTAL_LOWER_LIMIT:
							if(AlarmLevelEnum.LEVEL_ONE == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getFirstThresholdLower());
							}
							if(AlarmLevelEnum.LEVEL_TWO == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getSecondThresholdLower());
							}
							if(AlarmLevelEnum.LEVEL_THREE == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getThirdThresholdLower());
							}
							if(AlarmLevelEnum.LEVEL_FOUR == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getFourthThresholdLower());
							}
							map.put(alarmThreshold.getData().getAlarmType() + StringPool.COLON
								+ alarmRecord.getData().getAlarmLevel(), list);
							break;
						case INSIDE_WINDOW:
						case OUTSIDE_WINDOW:
							if(AlarmLevelEnum.LEVEL_ONE == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getFirstThresholdLower());
								list.add(alarmThreshold.getData().getFirstThresholdUpper());
							}
							if(AlarmLevelEnum.LEVEL_TWO == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getSecondThresholdLower());
								list.add(alarmThreshold.getData().getSecondThresholdUpper());
							}
							if(AlarmLevelEnum.LEVEL_THREE == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getThirdThresholdLower());
								list.add(alarmThreshold.getData().getThirdThresholdUpper());
							}
							if(AlarmLevelEnum.LEVEL_FOUR == AlarmLevelEnum.getByCode(alarmLevel)){
								list.add(alarmThreshold.getData().getFourthThresholdLower());
								list.add(alarmThreshold.getData().getFourthThresholdUpper());
							}
							map.put(alarmThreshold.getData().getAlarmType() + StringPool.COLON
								+ alarmRecord.getData().getAlarmLevel(), list);
							break;
						default:
					}
					return map;
				}
			}
		}
		return map;
	}

	@Override
	public boolean removeByEquipment(Long equipmentId) {
		return baseMapper.removeByEquipment(equipmentId, null) >= 0;
	}

	@Override
	public boolean removeByMonitor(Long monitorId) {
		return baseMapper.removeByMonitor(monitorId, null) >= 0;
	}

	@Override
	public boolean removeBySensorCode(String sensorCode) {
		return baseMapper.removeBySensorCode(sensorCode, null) >= 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public long copyConfig(CopyConfigVO vo) {
		//1、根据是否同类型设备查询需要复制的设备列表
		List<Long> equipmentIdList = new ArrayList<>();
		if (vo.getOnlyCurrentEquipment() == 0) {
			equipmentIdList = equipmentService.getIdListOfSameCategory(vo.getEquipmentId());
		} else {
			equipmentIdList.add(vo.getEquipmentId());
		}
		//2、查询绑定到equipmentIdList的所有同类型的实例化传感器
		List<SensorInstance> sensorInstanceList = sensorInstanceService.getSameTypeSensorCodeList(vo.getSensorCode(), equipmentIdList);
		List<String> sensorCodeList = sensorInstanceList.stream()
			.map(SensorInstance::getCode)
			.distinct()
			.collect(Collectors.toList());
		//移除选中的sensorCode -> 需要复制的传感器列表：如果为空，则直接返回！
		sensorCodeList.remove(vo.getSensorCode());
		if (CollectionUtil.isEmpty(sensorCodeList)) {
			log.info("copyConfig() - 无需复制的传感器！");
			return 0;
		}
		//3、查询需要复制的传感器实例参数配置

		//4、查询所选卡片的配置(sensorCode + category)
		List<DeviceModelConfig> originalConfigList = baseMapper.selectList(new QueryWrapper<DeviceModelConfig>().lambda()
			.eq(DeviceModelConfig::getSensorCode, vo.getSensorCode())
			.eq(DeviceModelConfig::getCategory, vo.getCategory()));
		//5、复制卡片：删除旧的、插入新的
		//5.1 删除旧的
		int deleteCount = baseMapper.delete(new QueryWrapper<DeviceModelConfig>().lambda()
			.in(DeviceModelConfig::getSensorCode, sensorCodeList)
			.eq(DeviceModelConfig::getCategory, vo.getCategory()));
		log.info("copyConfig() - 删除旧的卡片配置，条数 = {}", deleteCount);
		//5.2 插入新的
		List<DeviceModelConfig> newConfigList = new ArrayList<>();
		sensorCodeList.forEach(sensorCode -> {
			SensorInstance sensorInstance = sensorInstanceService.getOne(Wrappers.<SensorInstance>query().lambda()
				.eq(SensorInstance::getCode, sensorCode));
			originalConfigList.forEach(originalConfig -> {
				SensorInstanceParam sensorInstanceParam = sensorInstanceParamService.getOne(Wrappers.<SensorInstanceParam>query().lambda()
					.eq(SensorInstanceParam::getInstanceId, sensorInstance.getId())
					.eq(SensorInstanceParam::getSampleDataType, originalConfig.getSampleDataType())
					.isNull(SensorInstanceParam::getFeature).orderByAsc(SensorInstanceParam::getAxialDirection).last("limit 1"));
				if (Func.isNotEmpty(sensorInstanceParam)) {
					// 复制卡片配置
					DeviceModelConfig deviceModelConfig = new DeviceModelConfig();
					BeanUtil.copyProperties(originalConfig, deviceModelConfig);
					deviceModelConfig.setEquipmentId(sensorInstance.getEquipmentId())
						.setMonitorId(sensorInstance.getMonitorId())
						.setSensorCode(sensorCode)
						.setSensorParamId(sensorInstanceParam.getId())
						.setSensorParamName(sensorInstanceParam.getParamName());
					// 清空id，更新通用字段
					deviceModelConfig.setId(null).setCreateUser(AuthUtil.getUserId()).setCreateTime(DateUtil.now());
					newConfigList.add(deviceModelConfig);
				}
			});
		});
		//批量插入
		if (CollectionUtil.isNotEmpty(newConfigList)) {
			boolean saveBatch = saveBatch(newConfigList);
			log.info("copyConfig() - 批量插入完毕！saveBatch = {}", saveBatch);
		}
		// 成功复制的卡片数 - sensorCode数
		long copyCount = newConfigList.stream().map(DeviceModelConfig::getSensorCode).distinct().count();
		log.info("copyConfig() - 复制完毕！总共复制了{}个{}卡片配置！", copyCount, DimensionCategoryEnum.getByCode(vo.getCategory()).getName());
		return copyCount;
	}

}
