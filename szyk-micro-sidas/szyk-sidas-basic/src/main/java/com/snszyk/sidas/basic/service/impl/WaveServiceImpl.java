package com.snszyk.sidas.basic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.sidas.basic.entity.Wave;
import com.snszyk.sidas.basic.mapper.WaveMapper;
import com.snszyk.sidas.basic.service.IWaveService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 传感器波形表 服务实现类
 *  <AUTHOR>
 */
@Service
@AllArgsConstructor
public class WaveServiceImpl extends ServiceImpl<WaveMapper, Wave> implements IWaveService {

	@Override
	public int removeAlarmThresholdByWave(List<Long> waveIdList) {
		return baseMapper.removeAlarmThresholdByWave(waveIdList);
	}

	@Override
	public int unbindWaveByIds(List<Long> waveIdList) {
		return baseMapper.unbindWaveByIds(waveIdList);
	}
}
