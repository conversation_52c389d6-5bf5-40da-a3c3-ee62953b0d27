#服务器端口
server:
  port: 8166

spring:
  jackson:
    time-zone: Asia/Shanghai
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: sidas_show
        group: SIDAS_GROUP
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        namespace: sidas_show
        group: SIDAS_GROUP
python:
  server: http://sidas-algorithm.sidas-dev

# 根据振动波形的有效值判断设备运行状态
szyk:
  eolm:
    enable-running-by-vibration: true
    running-state-min: 0.3
    sample-data-exception-time: 1

