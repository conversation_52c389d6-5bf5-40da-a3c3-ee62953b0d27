/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备点检统计数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
@Accessors(chain = true)
public class StatisticsSearchVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 点检部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "点检部门")
	private Long inspectDept;

	/**
	 * 点检人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "点检人")
	private Long inspectUser;

	/**
	 * 按时间查询（0：近7天；1：近30天；2：近一年）
	 */
	@ApiModelProperty(value = "按时间查询（0：近7天；1：近30天；2：近一年）")
	private Integer queryDate;

	/**
	 * 筛选条件-按月查询
	 */
	@ApiModelProperty(value = "筛选条件-按月查询")
	private String queryMonth;

	/**
	 * 排名前几
	 */
	@ApiModelProperty(value = "排名前几")
	private Integer rank;


}
