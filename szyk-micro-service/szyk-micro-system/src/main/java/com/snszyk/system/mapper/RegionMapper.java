/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.snszyk.system.entity.Region;
import com.snszyk.system.excel.RegionExcel;
import com.snszyk.system.vo.RegionVO;

import java.util.List;
import java.util.Map;

/**
 * 行政区划表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface RegionMapper extends BaseMapper<Region> {

	/**
	 * 懒加载列表
	 *
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<RegionVO> lazyList(String parentCode, Map<String, Object> param);

	/**
	 * 懒加载列表
	 *
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<RegionVO> lazyTree(String parentCode, Map<String, Object> param);

	/**
	 * 导出区划数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<RegionExcel> exportRegion(@Param("ew") Wrapper<Region> queryWrapper);

}
