package com.snszyk.message.dto;

import com.snszyk.message.entity.Message;
import com.snszyk.message.vo.ReceiverInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息dto
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class MessageDto extends Message {

	private static final long serialVersionUID = 1L;

	/**
	 * 接收人信息
	 */
	@ApiModelProperty("接收人信息")
	private ReceiverInfoVo receiverInfoVo;

}
