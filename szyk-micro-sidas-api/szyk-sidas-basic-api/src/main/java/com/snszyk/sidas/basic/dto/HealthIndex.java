package com.snszyk.sidas.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备健康指数详情
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "HealthIndex对象", description = "HealthIndex对象")
public class HealthIndex implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "健康指数")
	private Integer healthIndex;

	@ApiModelProperty(value = "报警等级得分")
	private Integer alarmIndex;

	@ApiModelProperty(value = "报警等级")
	private Integer alarmLevel;

	@ApiModelProperty(value = "设备使用年限得分")
	private Integer lifeIndex;

	@ApiModelProperty(value = "设备使用百分比")
	private BigDecimal usePercentage;

	@ApiModelProperty(value = "故障记录得分")
	private Integer faultIndex;

	@ApiModelProperty(value = "故障记录数")
	private Integer faultAmount;
}
