/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.fault.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 故障管理业务表实体类
 *
 * <AUTHOR>
 * @since 2022-12-05
 */
@Data
@Accessors(chain = true)
@TableName("eolm_fault_biz")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FaultBiz对象", description = "故障管理业务表")
public class FaultBiz extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 关联报警
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("关联报警")
	private Long alarmId;
	/**
	 * 关联异常
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("关联异常")
	private Long abnormalId;
	/**
	 * 单据编号
	 */
	@ApiModelProperty(value = "单据编号")
	private String bizNo;
	/**
	 * 单据名称
	 */
	@ApiModelProperty(value = "单据名称")
	private String bizName;
	/**
	 * 故障等级（字典：fault_level）
	 */
	@ApiModelProperty(value = "故障等级（字典：fault_level）")
	private String faultLevel;
	/**
	 * 设备主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备主键")
	private Long equipmentId;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	/**
	 * 设备路径id
	 */
	@ApiModelProperty(value = "设备路径id")
	private String devicePath;
	/**
	 * 设备路径名称
	 */
	@ApiModelProperty(value = "设备路径")
	private String pathName;
	/**
	 * 故障部位
	 */
	@ApiModelProperty(value = "故障部位")
	private String faultPosition;
	/**
	 * 故障类型
	 */
	@ApiModelProperty(value = "故障类型")
	private String faultType;
	/**
	 * 诊断时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "诊断时间")
	private Date diagnoseTime;
	/**
	 * 诊断结论
	 */
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;
	/**
	 * 检维修建议
	 */
	@ApiModelProperty(value = "检维修建议")
	private String suggestion;
	/**
	 * 接收人
	 */
	@ApiModelProperty(value = "接收人")
	private String receiver;
	/**
	 * 是否生成故障案例
	 */
	@ApiModelProperty(value = "是否生成故障案例")
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer isCase;
	/**
	 * 故障案例编号
	 */
	@ApiModelProperty(value = "故障案例编号")
	private String caseNo;
	/**
	 * 关闭时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "关闭时间")
	private Date closeTime;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
