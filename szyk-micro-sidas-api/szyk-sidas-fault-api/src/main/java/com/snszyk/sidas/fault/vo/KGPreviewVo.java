package com.snszyk.sidas.fault.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "KGPreviewVo对象", description = "知识图谱预览传参")
public class KGPreviewVo implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "命令")
	private String command;

	@ApiModelProperty(value = "图谱id")
	private String graphId;

}
