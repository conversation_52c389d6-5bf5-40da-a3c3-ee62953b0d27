/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 诊断报告表实体类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@Accessors(chain = true)
@TableName("eolm_diagnostic_report")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosticReport对象", description = "诊断报告表")
public class DiagnosticReport extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 报告名称
	 */
	@ApiModelProperty(value = "报告名称")
	private String name;
	/**
	 * 报告编号
	 */
	@ApiModelProperty(value = "报告编号")
	private String reportNo;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 诊断id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "诊断id")
	private Long diagnosisId;
	/**
	 * 诊断报告
	 */
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;
	/**
	 * 检维修建议
	 */
	@ApiModelProperty(value = "检维修建议")
	private String suggestion;
	/**
	 * 趋势图
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "趋势图")
	private String trendChart;
	/**
	 * 波形图
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "波形图")
	private String waveForm;
	/**
	 * 诊断时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "诊断时间")
	private Date diagnoseTime;

}
