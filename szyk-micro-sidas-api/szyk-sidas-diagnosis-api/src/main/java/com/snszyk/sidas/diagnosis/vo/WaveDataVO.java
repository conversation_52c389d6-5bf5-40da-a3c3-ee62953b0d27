/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 波形数据
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@ApiModel(value = "WaveDataVO对象", description = "WaveDataVO")
public class WaveDataVO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "传感器id")
	private Long id;

	@ApiModelProperty(value = "采样时间")
	private Long time;

	@ApiModelProperty(value = "采样点数")
	private Integer number;

	@ApiModelProperty(value = "查询类型(0:all, 1:single)")
	private String type;

	@ApiModelProperty(value = "开始时间")
	private String startDate;

	@ApiModelProperty(value = "结束时间")
	private String endDate;

	public WaveDataVO(Long id, Long time, Integer number) {
		this.id = id;
		this.time = time;
		this.number = number;
	}

}
