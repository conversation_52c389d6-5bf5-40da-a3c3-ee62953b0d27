/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警类型枚举类
 *
 * <AUTHOR>
 * @date 2022/12/13 15:10
 **/
@Getter
@AllArgsConstructor
public enum AlarmBizTypeEnum {

	/**
	 * 智能
	 */
	INTELLIGENCE(0, "AI"),
	/**
	 * 门限
	 */
	THRESHOLD(1, "门限"),
	/**
	 * 机理
	 */
	MECHANISM(2, "机理"),

	/**
	 * 未知
	 */
	NOT_SUPPORTED(Integer.MAX_VALUE, "未知"),
	;

	final Integer code;
	final String name;

	public static AlarmBizTypeEnum getByCode(Integer code){
		for (AlarmBizTypeEnum value : AlarmBizTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return NOT_SUPPORTED;
	}

}
