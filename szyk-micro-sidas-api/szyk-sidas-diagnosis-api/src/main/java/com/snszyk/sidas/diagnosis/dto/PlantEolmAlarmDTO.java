package com.snszyk.sidas.diagnosis.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 厂区设备报警概况
 * @ClassName: PlantEolmAlarmDTO
 * @author: wangmh
 * @create: 2022-12-15 11:21
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlantEolmAlarmDTO {

	/**
	 * 厂区名称
	 */
	private String plantName;

	/**
	 * 正常
	 */
	private Integer normal;

	/**
	 * 报警数量
	 */
	private Integer alarm;
}
