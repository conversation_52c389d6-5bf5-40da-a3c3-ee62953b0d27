/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.sidas.diagnosis.entity.AbnormalRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备异常明细表视图实体类
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AbnormalRecordVO对象", description = "设备异常明细表")
public class AbnormalRecordVO extends AbnormalRecord {
	private static final long serialVersionUID = 1L;

	public AbnormalRecordVO() {
		super();
	}

	public AbnormalRecordVO(Long waveId) {
		super();
		this.setWaveId(waveId);
	}

	public AbnormalRecordVO(Integer strategyType, Integer abnormalType, Date abnormalTime) {
		super();
		this.setStrategyType(strategyType);
		this.setAbnormalType(abnormalType);
		this.setAbnormalTime(abnormalTime);
	}

}
