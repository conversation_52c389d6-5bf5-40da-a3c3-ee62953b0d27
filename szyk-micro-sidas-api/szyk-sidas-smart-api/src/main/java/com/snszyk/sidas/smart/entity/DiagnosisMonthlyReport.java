package com.snszyk.sidas.smart.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("eolm_diagnosis_monthly_report")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosisMonthlyReport对象", description = "月度诊断报告")
public class DiagnosisMonthlyReport extends TenantEntity {

	private static final long serialVersionUID = 1L;


}
